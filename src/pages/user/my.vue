<template>
  <view class="my-page">
    <!-- 自定义导航栏 -->
    <CustomNavBar
      title="我的"
      :show-back="false"
      background-color="transparent"
      title-color="#333333"
    />

    <!-- 顶部背景区域 -->
    <view class="header-bg">
      <!-- 用户信息卡片 -->
      <UserInfoCard
        :user-info="userInfo"
        @click="handleUserInfoClick"
        class="user-info-card"
      />
    </view>

    <!-- 统计和订单合并区域 -->
    <view class="combined-section">
      <!-- 认养统计卡片 -->
      <MyAdoptionCard :adoption-stats="adoptionStats" @click="handleAdoptionClick" />

      <!-- 订单状态区域 -->
      <OrderActions :order-status="orderStatus" @order-click="handleOrderClick" />
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-section">
      <view class="menu-list">
        <!-- 普通菜单项 -->
        <!-- 菜单项 -->
        <MenuListItem
          :icon="normalMenuList[0].icon"
          :title="normalMenuList[0].title"
          @click="handleNormalMenuClick(0)"
          class="menu-item"
        />
        <MenuListItem
          :icon="normalMenuList[1].icon"
          :title="normalMenuList[1].title"
          @click="handleNormalMenuClick(1)"
          class="menu-item"
        />
        <view class="menu-item">
          <button class="contact-service-menu" open-type="contact">
            <view class="item-content">
              <view class="icon-container">
                <image
                  src="/static/contact-service-2.svg"
                  class="menu-icon"
                  mode="aspectFit"
                />
              </view>
              <text class="menu-title">联系客服</text>
              <view class="arrow-container">
                <image
                  src="/static/icons/arrow-right.svg"
                  class="arrow-icon"
                  mode="aspectFit"
                />
              </view>
            </view>
          </button>
        </view>
        <MenuListItem
          :icon="aboutUsMenu.icon"
          :title="aboutUsMenu.title"
          @click="handleAboutUsClick"
          class="menu-item"
        />
      </view>

      <!-- 退出登录按钮 -->
      <LogoutButton @click="handleLogout" />
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import CustomNavBar from '@/components/CustomNavBar.vue'
import UserInfoCard from './components/UserInfoCard.vue'
import MyAdoptionCard from './components/MyAdoptionCard.vue'
import OrderActions from './components/OrderActions.vue'
import MenuListItem from './components/MenuListItem.vue'
import LogoutButton from './components/LogoutButton.vue'

// 用户store
const userStore = useUserStore()

// 使用 store 中的用户信息
const userInfo = computed(() => userStore.userInfo || {})

// 认养统计数据
const adoptionStats = computed(() => {
  return {
    treeCount: userInfo.value.treeCount || 0,
    equityCount: userInfo.value.benefitCount || 0
  }
})

// 订单状态
const orderStatus = ref([
  { icon: '/static/order-all.svg', title: '全部订单' },
  { icon: '/static/order-unpay.svg', title: '待付款' },
  { icon: '/static/order-wait-deliver.svg', title: '待配送' },
  { icon: '/static/order-wait-receive.svg', title: '待收货' }
])

// 普通菜单项（不包括客服）
const normalMenuList = ref([
  { icon: '/static/address.svg', title: '收货地址' },
  { icon: '/static/business-cooperation.svg', title: '商务合作' }
])

// 关于我们菜单项
const aboutUsMenu = ref({
  icon: '/static/adoption-benefits.svg',
  title: '关于我们'
})

// 初始化用户信息
const initUserInfo = async () => {
  // 如果 store 中没有用户信息，则获取
  if (!userStore.userInfo) {
    try {
      await userStore.refreshUserInfo()
    } catch (error) {
      console.error('获取用户信息失败', error)
      uni.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      })
    }
  }
}

onShow(() => {
  // 只在用户信息为空时才获取
  initUserInfo()
})

// 处理订单状态点击
const handleOrderClick = (index) => {
  const routes = [
    '/pages/order/order',
    '/pages/order/order?status=1',
    '/pages/order/order?status=2',
    '/pages/order/order?status=3'
  ]
  uni.navigateTo({
    url: routes[index]
  })
}

// 处理用户信息点击
const handleUserInfoClick = () => {
  uni.navigateTo({
    url: '/pages/user/profile/profile'
  })
}

// 处理认养卡片点击
const handleAdoptionClick = () => {
  uni.navigateTo({
    url: '/pages/tree/adoption/adoption'
  })
}

// 处理普通菜单点击
const handleNormalMenuClick = (index) => {
  const routes = [
    '/pages/address/list/list',
    '/pages/company/business-cooperation/business-cooperation'
  ]

  uni.navigateTo({
    url: routes[index]
  })
}

// 处理关于我们点击
const handleAboutUsClick = () => {
  uni.navigateTo({
    url: '/pages/company/about-us/about-us'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 使用store的logout方法
        userStore.logout()
      }
    }
  })
}
</script>

<style>
page {
  background-color: #f5f5f5;
}
</style>

<style lang="scss" scoped>
// 颜色变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #666666;
$text-light: #767676;
$bg-gray: #f5f5f5;
$border-gray: #e9e9e9;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin card-style {
  background-color: $white-color;
  border-radius: 20rpx;
}

.my-page {
  min-height: 100vh;
  background-color: $bg-gray;

  .header-bg {
    width: 100%;
    background: linear-gradient(180deg, #f9edd5 0%, #f6f6f6 97.86%);
    padding: 300rpx 32rpx 32rpx; // 增加顶部内边距，为导航栏和用户信息留出足够空间
    margin-top: -100rpx; // 向上偏移，让背景延伸到状态栏区域，保持沉浸式效果
    box-sizing: border-box;

    .user-info-card {
      width: 100%;
    }
  }

  .combined-section {
    margin: 0rpx 32rpx 36rpx;
    border-radius: 20rpx;
    background: linear-gradient(180deg, #f6e7c3 0%, #fbfaf3 71%);
    padding: 20rpx;
  }

  .menu-section {
    margin: 0 32rpx 36rpx;

    .menu-list {
      @include card-style;
      overflow: hidden;
      margin-bottom: 80rpx;
      padding: 0 32rpx;

      .menu-item {
        border-bottom: 1rpx solid $border-gray;

        &:last-child {
          border-bottom: none;
        }
      }

      .contact-service-menu {
        width: 100%;
        background: none;
        border: none;
        padding: 0;
        margin: 0;
        text-align: left;
        outline: none;

        &::after {
          border: none;
        }

        .item-content {
          height: 100rpx;
          display: flex;
          align-items: center;
          gap: 16rpx;

          .icon-container {
            width: 32rpx;
            height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .menu-icon {
              width: 100%;
              height: 100%;
            }
          }

          .menu-title {
            flex: 1;
            font-size: 30rpx;
            color: $text-dark;
            line-height: 35rpx;
          }

          .arrow-container {
            width: 48rpx;
            height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .arrow-icon {
              width: 48rpx;
              height: 48rpx;
            }
          }
        }
      }
    }
  }
}
</style>
